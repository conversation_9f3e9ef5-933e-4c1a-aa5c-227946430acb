<template>
  <div class="sales-return-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>销售退货单</h2>
        <p class="page-description">管理销售退货单，跟踪客户退货流程和质量问题</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="openCreateDialog">
          <el-icon><Plus /></el-icon>
          新建退货单
        </el-button>
        <el-button @click="exportReturns">
          <el-icon><Download /></el-icon>
          导出退货单
        </el-button>
      </div>
    </div>

    <!-- 退货单概览 -->
    <el-row :gutter="20" class="return-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><Tickets /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.total_returns }}</div>
              <div class="overview-label">退货单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon pending">
              <el-icon size="32"><Clock /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.draft_returns }}</div>
              <div class="overview-label">草稿</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon processing">
              <el-icon size="32"><Loading /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.submitted_returns }}</div>
              <div class="overview-label">已提交</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon completed">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.approved_returns }}</div>
              <div class="overview-label">已审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="退货单号">
          <el-input
            v-model="searchForm.return_no"
            placeholder="请输入退货单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="销售出库单号">
          <el-input
            v-model="searchForm.sales_outbound_no"
            placeholder="请输入销售出库单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="客户">
          <el-select v-model="searchForm.customer_id" placeholder="选择客户" clearable style="width: 200px">
            <el-option
              v-for="customer in customers"
              :key="customer.id"
              :label="customer.name"
              :value="customer.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="单据状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="草稿" value="draft" />
            <el-option label="已提交" value="submitted" />
            <el-option label="已审核" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已退货" value="returned" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>

        <el-form-item label="退货日期">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="fetchSalesReturns" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 退货单列表 -->
    <el-card class="return-list-card">
      <template #header>
        <div class="card-header">
          <span>退货单列表</span>
          <div class="header-actions">
            <el-button-group>
              <el-button 
                :type="viewMode === 'table' ? 'primary' : ''" 
                @click="viewMode = 'table'"
                :icon="List"
              >
                表格视图
              </el-button>
              <el-button 
                :type="viewMode === 'card' ? 'primary' : ''" 
                @click="viewMode = 'card'"
                :icon="Grid"
              >
                卡片视图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="salesReturns" style="width: 100%" v-loading="loading">
          <el-table-column prop="return_no" label="退货单号" width="180" />
          <el-table-column prop="customer_name" label="客户" width="150" />
          <el-table-column prop="total_amount" label="退货金额" width="120">
            <template #default="{ row }">
              <span class="amount">¥{{ parseFloat(String(row.total_amount || 0)).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="return_date" label="退货日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.return_date) }}
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="退货原因" min-width="200" show-overflow-tooltip />
          <el-table-column prop="created_at" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click="viewReturnDetail(row)">查看</el-button>
                <el-button size="small" @click="submitReturn(row)" v-if="row.status === SalesReturnStatus.DRAFT" type="primary">提交</el-button>
                <el-button size="small" @click="revokeReturn(row)" v-if="row.status === SalesReturnStatus.SUBMITTED" type="warning">撤销</el-button>
                <el-dropdown @command="(command: string) => handleReturnAction(command, row)" v-if="row.status !== SalesReturnStatus.COMPLETED && row.status !== SalesReturnStatus.REJECTED">
                  <el-button size="small" type="primary">
                    操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit" v-if="row.status === SalesReturnStatus.DRAFT">编辑</el-dropdown-item>
                      <el-dropdown-item command="approve" v-if="row.status === SalesReturnStatus.SUBMITTED">审核通过</el-dropdown-item>
                      <el-dropdown-item command="reject" v-if="row.status === SalesReturnStatus.SUBMITTED">审核拒绝</el-dropdown-item>
                      <el-dropdown-item command="return" v-if="row.status === SalesReturnStatus.APPROVED">确认退货</el-dropdown-item>
                      <el-dropdown-item command="complete" v-if="row.status === SalesReturnStatus.RETURNED">完成</el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="row.status === SalesReturnStatus.DRAFT" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col :span="8" v-for="returnItem in salesReturns" :key="returnItem.id">
            <el-card class="return-card" @click="viewReturnDetail(returnItem)">
              <div class="card-header">
                <h3>{{ returnItem.return_no }}</h3>
                <el-tag :type="getStatusTagType(returnItem.status)">
                  {{ getStatusLabel(returnItem.status) }}
                </el-tag>
              </div>
              <div class="card-content">
                <p><strong>客户:</strong> {{ returnItem.customer_name }}</p>
                <p><strong>退货金额:</strong> <span class="amount">¥{{ parseFloat(String(returnItem.total_amount || 0)).toFixed(2) }}</span></p>
                <p><strong>退货日期:</strong> {{ formatDate(returnItem.return_date) }}</p>
                <p><strong>退货原因:</strong> {{ returnItem.reason }}</p>
              </div>
              <div class="card-actions" @click.stop>
                <el-button size="small" @click="viewReturnDetail(returnItem)">查看详情</el-button>
                <el-button size="small" @click="submitReturn(returnItem)" v-if="returnItem.status === SalesReturnStatus.DRAFT" type="primary">提交</el-button>
                <el-button size="small" @click="revokeReturn(returnItem)" v-if="returnItem.status === SalesReturnStatus.SUBMITTED" type="warning">撤销</el-button>
                <el-dropdown @command="(command: string) => handleReturnAction(command, returnItem)" v-if="returnItem.status !== SalesReturnStatus.COMPLETED && returnItem.status !== SalesReturnStatus.REJECTED">
                  <el-button size="small" type="primary">
                    操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit" v-if="returnItem.status === SalesReturnStatus.DRAFT">编辑</el-dropdown-item>
                      <el-dropdown-item command="approve" v-if="returnItem.status === SalesReturnStatus.SUBMITTED">审核通过</el-dropdown-item>
                      <el-dropdown-item command="reject" v-if="returnItem.status === SalesReturnStatus.SUBMITTED">审核拒绝</el-dropdown-item>
                      <el-dropdown-item command="return" v-if="returnItem.status === SalesReturnStatus.APPROVED">确认退货</el-dropdown-item>
                      <el-dropdown-item command="complete" v-if="returnItem.status === SalesReturnStatus.RETURNED">完成</el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="returnItem.status === SalesReturnStatus.DRAFT" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchSalesReturns"
          @current-change="fetchSalesReturns"
        />
      </div>
    </el-card>

    <!-- 创建/编辑退货单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="isEditing ? '编辑退货单' : '新建退货单'"
      width="1000px"
      :close-on-click-modal="false"
      @close="resetReturnForm"
    >
      <el-form
        ref="returnFormRef"
        :model="returnForm"
        :rules="returnFormRules"
        label-width="100px"
      >
        <!-- 退货方式选择 -->
        <el-form-item label="退货方式">
          <el-radio-group v-model="returnForm.return_type" @change="onReturnTypeChange">
            <el-radio value="manual">手动创建</el-radio>
            <el-radio value="from_outbound">从销售出库单创建</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 选择销售出库单 -->
        <el-form-item
          v-if="returnForm.return_type === 'from_outbound'"
          label="销售出库单"
          prop="sales_outbound_id"
        >
          <el-select
            v-model="returnForm.sales_outbound_id"
            placeholder="选择销售出库单"
            style="width: 100%"
            filterable
            @change="onSalesOutboundChange"
            :loading="loadingOrders"
          >
            <el-option
              v-for="outbound in availableOutbounds"
              :key="outbound.id"
              :label="`${outbound.outbound_no} - ${outbound.customer_name} (${formatDate(outbound.outbound_date)})`"
              :value="outbound.id"
            />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="退货单号" prop="return_no">
              <el-input
                v-model="returnForm.return_no"
                placeholder="系统自动生成"
                readonly
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="returnForm.return_type === 'from_outbound'">
            <el-form-item label="销售出库单号">
              <el-input
                v-model="returnForm.sales_outbound_no"
                placeholder="关联的销售出库单号"
                readonly
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户" prop="customer_id">
              <el-select
                v-model="returnForm.customer_id"
                placeholder="选择客户"
                style="width: 100%"
                :disabled="returnForm.return_type === 'from_outbound'"
                filterable
              >
                <el-option
                  v-for="customer in customers"
                  :key="customer.id"
                  :label="customer.name"
                  :value="customer.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退货日期" prop="return_date">
              <el-date-picker
                v-model="returnForm.return_date"
                type="date"
                placeholder="选择退货日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="退货原因" prop="reason">
              <el-input
                v-model="returnForm.reason"
                placeholder="请输入退货原因"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input
                v-model="returnForm.remark"
                placeholder="请输入备注信息"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 商品明细 -->
        <el-form-item label="退货商品">
          <div style="width: 100%;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
              <span style="font-weight: 500;">商品明细</span>
              <el-button
                type="primary"
                size="small"
                @click="addReturnItem"
                v-if="returnForm.return_type === 'manual'"
              >
                <el-icon><Plus /></el-icon>
                添加商品
              </el-button>
              <div v-if="returnForm.return_type === 'from_outbound'" class="order-mode-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>从销售出库单创建模式：商品列表由所选销售出库单自动生成，不可手动添加</span>
              </div>
            </div>

            <div style="overflow-x: auto; width: 100%; margin-top: 10px; padding: 0 4px;">
              <div class="custom-table" style="min-width: 1200px;">
                <div class="table-header">
                  <div class="table-cell product">商品信息</div>
                  <div class="table-cell batch">批次号</div>
                  <div class="table-cell warehouse">仓库</div>
                  <div class="table-cell quantity">退货数量</div>
                  <div class="table-cell unit-price">单价</div>
                  <div class="table-cell total-price">小计</div>
                  <div class="table-cell quality">质量问题</div>
                  <div class="table-cell actions">操作</div>
                </div>

                <div v-for="(row, index) in returnForm.items" :key="index" class="table-row">
                  <!-- 1. 商品信息 -->
                  <div class="table-cell product">
                    <el-select
                      v-model="row.product_id"
                      placeholder="选择商品"
                      filterable
                      @change="onProductChange(row)"
                      :disabled="returnForm.return_type === 'from_outbound'"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="product in availableProducts"
                        :key="product.id"
                        :label="`${product.name} (${product.sku})`"
                        :value="product.id"
                      />
                    </el-select>
                  </div>

                  <!-- 2. 批次号（从出库单生成时固定，手动创建时可随意填写） -->
                  <div class="table-cell batch">
                    <el-input
                      v-if="returnForm.return_type === 'from_outbound'"
                      v-model="row.batch_no"
                      placeholder="出库单批次"
                      readonly
                      size="small"
                      style="width: 100%"
                    />
                    <el-input
                      v-else
                      v-model="row.batch_no"
                      placeholder="输入批次号"
                      size="small"
                      style="width: 100%"
                    />
                  </div>

                  <!-- 3. 仓库选择（退货商品要入库到哪个仓库） -->
                  <div class="table-cell warehouse">
                    <el-select
                      v-model="row.warehouse_id"
                      placeholder="选择入库仓库"
                      @change="onWarehouseChange(row)"
                      style="width: 100%"
                      size="small"
                    >
                      <el-option
                        v-for="warehouse in availableWarehouses"
                        :key="warehouse.id"
                        :label="warehouse.name"
                        :value="warehouse.id"
                      />
                    </el-select>
                  </div>

                  <!-- 4. 退货数量（合并显示可退数量和输入框） -->
                  <div class="table-cell quantity">
                    <div class="quantity-container">
                      <!-- 从出库单生成时显示可退数量提示 -->
                      <div v-if="returnForm.return_type === 'from_outbound'" class="available-hint">
                        可退: {{ row.available_quantity || 0 }}
                      </div>
                      <el-input-number
                        v-model="row.return_quantity"
                        :min="0"
                        :max="returnForm.return_type === 'from_outbound' ? row.available_quantity : undefined"
                        style="width: 100%"
                        size="small"
                        :placeholder="returnForm.return_type === 'from_outbound' ? `最多${row.available_quantity || 0}` : '请输入数量'"
                        :disabled="!row.warehouse_id"
                        @change="calculateItemTotal(row)"
                      />
                    </div>
                  </div>

                  <div class="table-cell unit-price">
                    <el-input-number
                      v-model="row.unit_price"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                      size="small"
                      placeholder="单价"
                      @change="calculateItemTotal(row)"
                    />
                  </div>

                  <div class="table-cell total-price">
                    <span class="price-display">¥{{ parseFloat(String(row.total_price || 0)).toFixed(2) }}</span>
                  </div>

                  <div class="table-cell quality">
                    <el-input
                      v-model="row.quality_issue"
                      placeholder="质量问题描述"
                      size="small"
                    />
                  </div>

                  <div class="table-cell actions">
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeReturnItem(index)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>

                <div v-if="returnForm.items.length === 0" class="empty-state">
                  <p>暂无商品明细</p>
                  <el-button type="primary" @click="addReturnItem" v-if="returnForm.return_type === 'manual'">
                    添加第一个商品
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 总计 -->
            <div class="total-section">
              <span class="total-label">退货总金额：</span>
              <span class="total-amount">¥{{ parseFloat(String(returnForm.total_amount || 0)).toFixed(2) }}</span>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveReturn" :loading="saving">
            {{ isEditing ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 退货单详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="退货单详情" width="800px">
      <div v-if="selectedReturn">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="退货单号">{{ selectedReturn.return_no }}</el-descriptions-item>
          <el-descriptions-item label="客户">{{ selectedReturn.customer_name }}</el-descriptions-item>
          <el-descriptions-item label="关联销售出库单">{{ selectedReturn.sales_outbound_no || '-' }}</el-descriptions-item>
          <el-descriptions-item label="退货日期">{{ formatDate(selectedReturn.return_date) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedReturn.status)">
              {{ getStatusLabel(selectedReturn.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="退货金额">
            <span class="amount">¥{{ parseFloat(String(selectedReturn.total_amount || 0)).toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="退货原因" :span="2">{{ selectedReturn.reason }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ selectedReturn.remark || '-' }}</el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px;">
          <h4>退货商品明细</h4>
          <el-table :data="selectedReturn.items" border style="margin-top: 10px;">
            <el-table-column prop="product_name" label="商品名称" min-width="150" />
            <el-table-column prop="product_sku" label="商品SKU" width="120" />
            <el-table-column prop="batch_no" label="批次号" width="120">
              <template #default="{ row }">
                {{ row.batch_no || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="warehouse_id" label="仓库" width="100">
              <template #default="{ row }">
                {{ getWarehouseName(row.warehouse_id) }}
              </template>
            </el-table-column>
            <el-table-column prop="return_quantity" label="退货数量" width="100" />
            <el-table-column prop="unit_price" label="单价" width="100">
              <template #default="{ row }">
                ¥{{ parseFloat(String(row.unit_price || 0)).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_price" label="小计" width="100">
              <template #default="{ row }">
                ¥{{ parseFloat(String(row.total_price || 0)).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="quality_issue" label="质量问题" min-width="120">
              <template #default="{ row }">
                {{ row.quality_issue || '-' }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  salesReturnApi,
  salesOutboundApi,
  type SalesReturn,
  type SalesReturnCreate,
  type SalesReturnUpdate,
  type SalesReturnStats,
  type SalesOutbound,
  SalesReturnStatus
} from '@/api/sales'
import { customerApi, type Customer } from '@/api/customers'
import { productApi, type Product } from '@/api/products'
import { warehouseApi, type Warehouse } from '@/api/warehouses'
import {
  Plus,
  Download,
  Tickets,
  Clock,
  Loading,
  CircleCheck,
  Search,
  List,
  Grid,
  ArrowDown,
  Refresh,
  InfoFilled
} from '@element-plus/icons-vue'

// 响应式数据
const salesReturns = ref<SalesReturn[]>([])
const customers = ref<Customer[]>([])
const availableOutbounds = ref<SalesOutbound[]>([])
const availableProducts = ref<Product[]>([])
const availableWarehouses = ref<Warehouse[]>([])
const loading = ref(false)
const loadingOrders = ref(false)
const saving = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedReturn = ref<SalesReturn | null>(null)
const isEditing = ref(false)
const returnFormRef = ref()

// 退货单表单
const returnForm = reactive({
  return_type: 'manual' as 'manual' | 'from_outbound',
  return_no: '' as string,
  customer_id: undefined as number | undefined,
  sales_outbound_id: undefined as number | undefined,
  sales_outbound_no: '' as string,
  return_date: '' as string,
  reason: '' as string,
  remark: '' as string,
  total_amount: 0,
  items: [] as any[]
})

// 表单验证规则
const returnFormRules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  return_date: [
    { required: true, message: '请选择退货日期', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入退货原因', trigger: 'blur' }
  ],
  sales_outbound_id: [
    {
      required: false,
      message: '请选择销售出库单',
      trigger: 'change',
      validator: (_rule: any, value: any, callback: any) => {
        if (returnForm.return_type === 'from_outbound' && !value) {
          callback(new Error('请选择销售出库单'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 搜索表单
const searchForm = reactive({
  return_no: '',
  sales_outbound_no: '',
  customer_id: undefined as number | undefined,
  status: '',
  date_range: undefined as [Date, Date] | undefined
})

// 统计数据
const returnStats = ref<SalesReturnStats>({
  total_returns: 0,
  draft_returns: 0,
  submitted_returns: 0,
  approved_returns: 0,
  returned_returns: 0,
  completed_returns: 0,
  total_amount: 0
})

// 方法
const fetchSalesReturns = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value,
      sort_by: 'created_at',
      sort_order: 'desc'
    }

    if (searchForm.return_no) {
      params.return_no = searchForm.return_no
    }
    if (searchForm.customer_id) {
      params.customer_id = searchForm.customer_id
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.date_range) {
      params.start_date = searchForm.date_range[0].toISOString()
      params.end_date = searchForm.date_range[1].toISOString()
    }

    const response = await salesReturnApi.getSalesReturns(params)

    if (response) {
      salesReturns.value = response || []
      total.value = response.length || 0
    } else {
      salesReturns.value = []
      total.value = 0
    }

    // 获取统计数据
    const statsResponse = await salesReturnApi.getStats()
    if (statsResponse) {
      returnStats.value = statsResponse
    }
  } catch (error: any) {
    console.error('获取销售退货单失败:', error)
    ElMessage.error('获取销售退货单失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

const fetchCustomers = async () => {
  try {
    const response = await customerApi.getCustomers()
    if (response && (response as any).items) {
      customers.value = (response as any).items
    }
  } catch (error: any) {
    console.error('获取客户列表失败:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchSalesReturns()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    return_no: '',
    sales_outbound_no: '',
    customer_id: undefined,
    status: '',
    date_range: undefined
  })
  handleSearch()
}

const viewReturnDetail = async (returnItem: SalesReturn) => {
  try {
    loading.value = true

    // 获取完整的退货单详情（包含明细）
    const detail = await salesReturnApi.getSalesReturn(returnItem.id!)
    console.log('获取到的退货单详情:', detail)
    console.log('明细数据:', detail.items)
    selectedReturn.value = detail

    // 确保仓库列表已加载（用于显示仓库名称）
    if (availableWarehouses.value.length === 0) {
      await fetchAvailableWarehouses()
    }

    showDetailDialog.value = true
  } catch (error: any) {
    console.error('获取退货单详情失败:', error)
    ElMessage.error('获取退货单详情失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

const handleReturnAction = async (command: string, returnItem: SalesReturn) => {
  switch (command) {
    case 'edit':
      await editReturn(returnItem)
      break
    case 'approve':
      await approveReturn(returnItem)
      break
    case 'reject':
      await rejectReturn(returnItem)
      break
    case 'return':
      await confirmReturn(returnItem)
      break
    case 'complete':
      await completeReturn(returnItem)
      break
    case 'delete':
      await deleteReturn(returnItem)
      break
    default:
      ElMessage.warning('未知操作')
  }
}

const submitReturn = async (returnItem: SalesReturn) => {
  try {
    await ElMessageBox.confirm('确定要提交这个退货单吗？', '确认提交', {
      type: 'warning'
    })

    await salesReturnApi.submitSalesReturn(returnItem.id!)
    ElMessage.success('退货单提交成功')
    fetchSalesReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('提交退货单失败:', error)
      ElMessage.error('提交失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const approveReturn = async (returnItem: SalesReturn) => {
  try {
    await ElMessageBox.confirm('确定要审核通过这个退货单吗？', '确认审核', {
      type: 'warning'
    })

    await salesReturnApi.approveSalesReturn(returnItem.id!, {
      status: SalesReturnStatus.APPROVED
    })
    ElMessage.success('退货单审核通过')
    fetchSalesReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('审核退货单失败:', error)
      ElMessage.error('审核失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const rejectReturn = async (returnItem: SalesReturn) => {
  try {
    const { value: rejectReason } = await ElMessageBox.prompt('请输入拒绝原因', '审核拒绝', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '拒绝原因不能为空'
    })

    await salesReturnApi.approveSalesReturn(returnItem.id!, {
      status: SalesReturnStatus.REJECTED,
      note: rejectReason
    })
    ElMessage.success('退货单审核拒绝')
    fetchSalesReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('拒绝退货单失败:', error)
      ElMessage.error('拒绝失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const confirmReturn = async (returnItem: SalesReturn) => {
  try {
    await ElMessageBox.confirm('确定要确认退货吗？', '确认退货', {
      type: 'warning'
    })

    await salesReturnApi.returnSalesReturn(returnItem.id!)
    ElMessage.success('退货确认成功')
    fetchSalesReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('确认退货失败:', error)
      ElMessage.error('确认退货失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const completeReturn = async (returnItem: SalesReturn) => {
  try {
    await ElMessageBox.confirm('确定要完成这个退货单吗？', '确认完成', {
      type: 'warning'
    })

    await salesReturnApi.completeSalesReturn(returnItem.id!)
    ElMessage.success('退货单完成')
    fetchSalesReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('完成退货单失败:', error)
      ElMessage.error('完成失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const deleteReturn = async (returnItem: SalesReturn) => {
  try {
    await ElMessageBox.confirm('确定要删除这个退货单吗？删除后无法恢复！', '确认删除', {
      type: 'warning'
    })

    await salesReturnApi.deleteSalesReturn(returnItem.id!)
    ElMessage.success('退货单删除成功')
    fetchSalesReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除退货单失败:', error)
      ElMessage.error('删除失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const revokeReturn = async (returnItem: SalesReturn) => {
  try {
    await ElMessageBox.confirm('确定要撤销这个退货单吗？撤销后将回到草稿状态。', '确认撤销', {
      type: 'warning'
    })

    await salesReturnApi.revokeSalesReturn(returnItem.id!)
    ElMessage.success('退货单撤销成功')
    fetchSalesReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('撤销退货单失败:', error)
      ElMessage.error('撤销失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

// 编辑退货单
const editReturn = async (returnItem: SalesReturn) => {
  try {
    loading.value = true

    // 获取退货单详情
    const detail = await salesReturnApi.getSalesReturn(returnItem.id!)

    // 填充表单数据
    isEditing.value = true
    selectedReturn.value = detail

    // 设置基本信息
    returnForm.return_no = detail.return_no
    returnForm.customer_id = detail.customer_id
    returnForm.sales_outbound_id = detail.sales_outbound_id || undefined
    returnForm.sales_outbound_no = detail.sales_outbound_no || ''
    returnForm.return_date = detail.return_date ? new Date(detail.return_date).toISOString().split('T')[0] : ''
    returnForm.reason = detail.reason
    returnForm.remark = detail.remark || ''
    returnForm.total_amount = Number(detail.total_amount) || 0

    // 设置退货方式
    returnForm.return_type = detail.sales_outbound_id ? 'from_outbound' : 'manual'

    // 设置退货明细
    returnForm.items = detail.items.map(item => ({
      id: item.id,
      product_id: item.product_id,
      product_name: item.product_name,
      product_sku: item.product_sku || '',
      batch_no: item.batch_no || '',
      warehouse_id: item.warehouse_id || null,
      return_quantity: item.return_quantity,
      unit_price: Number(item.unit_price) || 0,
      total_price: Number(item.total_price) || 0,
      quality_issue: item.quality_issue || ''
    }))

    // 如果是从出库单退货，需要加载出库单信息
    if (detail.sales_outbound_id) {
      await fetchAvailableOutbounds()
    }

    showCreateDialog.value = true
  } catch (error: any) {
    console.error('获取退货单详情失败:', error)
    ElMessage.error('获取退货单详情失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

// ==================== 表单相关方法 ====================

// 退货方式变更处理
const onReturnTypeChange = (type: 'manual' | 'from_outbound') => {
  // 清空相关字段
  returnForm.sales_outbound_id = undefined
  returnForm.sales_outbound_no = ''
  returnForm.customer_id = undefined
  returnForm.items = []
  returnForm.total_amount = 0

  if (type === 'from_outbound') {
    // 获取可用的销售出库单
    fetchAvailableOutbounds()
  }
}

// 选择销售出库单时的处理
const onSalesOutboundChange = async (outboundId: number) => {
  if (!outboundId) return

  try {
    const outbound = availableOutbounds.value.find(o => o.id === outboundId)
    if (!outbound) return

    // 自动填充客户信息和销售出库单信息
    returnForm.sales_outbound_id = outbound.id
    returnForm.customer_id = outbound.customer_id
    returnForm.sales_outbound_no = outbound.outbound_no || ''

    // 自动填充商品明细（基于销售出库单的商品）
    returnForm.items = outbound.items?.map((item: any) => {
      const outboundQuantity = item.outbound_quantity || item.quantity || 0
      const returnedQuantity = item.returned_quantity || 0
      const availableQuantity = outboundQuantity - returnedQuantity

      return {
        product_id: item.product_id,
        product_name: item.product_name || '',
        product_sku: item.product_sku || '',
        batch_no: item.batch_no || '', // 从出库单获取批次号
        warehouse_id: null, // 仓库需要用户选择
        return_quantity: 0,
        unit_price: 0, // 出库单可能没有价格信息，需要从商品信息获取
        total_price: 0,
        quality_issue: '',
        outbound_quantity: outboundQuantity, // 出库数量
        returned_quantity: returnedQuantity, // 已退货数量
        available_quantity: availableQuantity, // 可退货数量 = 出库数量 - 已退货数量
        max_quantity: availableQuantity // 最大可退货数量
      }
    }) || []

    // 重新计算总金额
    calculateTotalAmount()

    console.log('📦 已选择销售出库单:', {
      outboundNo: outbound.outbound_no,
      customer: outbound.customer_name,
      itemsCount: returnForm.items.length
    })
  } catch (error) {
    console.error('选择销售出库单失败:', error)
    ElMessage.error('获取销售出库单信息失败')
  }
}

// 商品选择变更处理
const onProductChange = async (row: any) => {
  if (!row.product_id) return

  try {
    const product = availableProducts.value.find(p => p.id === row.product_id)
    if (product) {
      row.product_name = product.name || ''
      row.product_sku = product.sku || ''
      row.unit_price = product.price || 0
      calculateItemTotal(row)
    }
  } catch (error) {
    console.error('获取商品信息失败:', error)
    ElMessage.error('获取商品信息失败')
  }
}

// 批次选择变更处理（仅手动创建时使用）
const onBatchChange = (row: any) => {
  if (!row.batch_no) return

  // 批次变更后，可以在这里添加相关逻辑
  // 比如获取该批次的库存信息等
  console.log('批次变更:', row.batch_no)
}

// 仓库选择变更处理
const onWarehouseChange = (row: any) => {
  if (!row.warehouse_id) return

  // 仓库变更后，可以在这里添加相关逻辑
  // 比如获取该仓库的库存信息等
  console.log('仓库变更:', row.warehouse_id)
}

// 添加退货商品
const addReturnItem = () => {
  returnForm.items.push({
    product_id: null,
    product_name: '',
    product_sku: '',
    batch_no: '',
    warehouse_id: null,
    return_quantity: 0,
    unit_price: 0,
    total_price: 0,
    quality_issue: '',
    availableBatches: [] // 手动创建时的可用批次列表
  })
}

// 删除退货商品
const removeReturnItem = (index: number) => {
  // 在from_outbound模式下，仍然允许删除商品项
  returnForm.items.splice(index, 1)
  calculateTotalAmount()
}

// 计算单项小计
const calculateItemTotal = (row: any) => {
  const quantity = Number(row.return_quantity) || 0
  const price = Number(row.unit_price) || 0
  row.total_price = quantity * price
  calculateTotalAmount()
}

// 计算总金额
const calculateTotalAmount = () => {
  returnForm.total_amount = returnForm.items.reduce((sum, item) => {
    return sum + (Number(item.total_price) || 0)
  }, 0)
}

// 保存退货单
const saveReturn = async () => {
  try {
    // 表单验证
    await returnFormRef.value?.validate()

    if (returnForm.items.length === 0) {
      ElMessage.warning('请至少添加一个退货商品')
      return
    }

    saving.value = true

    if (isEditing.value) {
      // 更新现有退货单
      const updateData = {
        sales_outbound_id: returnForm.sales_outbound_id,
        customer_id: returnForm.customer_id!,
        return_date: returnForm.return_date || undefined,
        reason: returnForm.reason || undefined,
        remark: returnForm.remark || undefined,
        items: returnForm.items
      } as any

      await salesReturnApi.updateSalesReturn(selectedReturn.value?.id!, updateData)
      ElMessage.success('退货单更新成功')
    } else {
      // 新增退货单
      const createData = {
        sales_outbound_id: returnForm.sales_outbound_id,
        customer_id: returnForm.customer_id!,
        return_date: returnForm.return_date ? new Date(returnForm.return_date).toISOString() : new Date().toISOString(),
        reason: returnForm.reason || '退货',
        remark: returnForm.remark,
        total_amount: returnForm.total_amount,
        items: returnForm.items
      } as any

      await salesReturnApi.createSalesReturn(createData)
      ElMessage.success('退货单创建成功')
    }

    // 重新获取数据
    await fetchSalesReturns()

    showCreateDialog.value = false
    resetReturnForm()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 打开创建对话框
const openCreateDialog = () => {
  resetReturnForm()
  showCreateDialog.value = true
}

const resetReturnForm = () => {
  isEditing.value = false
  Object.assign(returnForm, {
    return_type: 'manual',
    return_no: '', // 清空退货单号，让后端自动生成
    customer_id: undefined,
    sales_outbound_id: undefined,
    sales_outbound_no: '',
    return_date: '',
    reason: '',
    remark: '',
    total_amount: 0,
    items: []
  })
  if (returnFormRef.value) {
    returnFormRef.value.clearValidate()
  }
}

// 获取可用的销售出库单
const fetchAvailableOutbounds = async () => {
  loadingOrders.value = true
  try {
    // 获取已完成的销售出库单
    const response = await salesOutboundApi.getAvailableOutboundsForReturn()
    availableOutbounds.value = response || []
  } catch (error) {
    console.error('获取销售出库单失败:', error)
    ElMessage.error('获取销售出库单失败')
  } finally {
    loadingOrders.value = false
  }
}

// 获取可用商品
const fetchAvailableProducts = async () => {
  try {
    const response = await productApi.getProducts({
      page: 1,
      page_size: 100
    })
    availableProducts.value = response.items || []
  } catch (error) {
    console.error('获取商品失败:', error)
    ElMessage.error('获取商品失败')
  }
}

// 获取可用仓库
const fetchAvailableWarehouses = async () => {
  try {
    const response = await warehouseApi.getWarehouses({
      is_active: true,
      status: 'active'
    })
    availableWarehouses.value = response || []
    console.log('获取仓库列表成功:', availableWarehouses.value.length, '个仓库')
  } catch (error) {
    console.error('获取仓库失败:', error)
    ElMessage.error('获取仓库失败')
    availableWarehouses.value = []
  }
}

// 根据仓库ID获取仓库名称
const getWarehouseName = (warehouseId: number | null | undefined): string => {
  if (!warehouseId) return '-'
  const warehouse = availableWarehouses.value.find(w => w.id === warehouseId)
  return warehouse ? warehouse.name : `仓库${warehouseId}`
}

const exportReturns = () => {
  ElMessage.success('退货单导出成功')
}

// 辅助方法
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    approved: '已审核',
    rejected: '已拒绝',
    returned: '已退货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    draft: 'info',
    submitted: 'warning',
    approved: 'success',
    rejected: 'danger',
    returned: 'success',
    completed: 'success',
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const formatDate = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchSalesReturns()
  fetchCustomers()
  fetchAvailableProducts()
  fetchAvailableWarehouses()
})
</script>

<style scoped>
.sales-return-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 退货单概览 */
.return-overview {
  margin-bottom: 12px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.pending {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.processing {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.completed {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 退货单列表卡片 */
.return-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.return-list-card .el-card__header {
  padding: 14px 20px !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 金额样式 */
.amount {
  font-weight: 600;
  color: #E6A23C;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.return-card {
  margin-bottom: 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.return-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.return-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.return-card .card-header h3 {
  margin: 0;
  color: #303133;
}

.return-card .card-content {
  margin-bottom: 16px;
}

.return-card .card-content p {
  margin: 8px 0;
  color: #606266;
}

.return-card .card-actions {
  display: flex;
  gap: 8px;
}

/* 表格操作 */
.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions .el-button {
  margin: 0;
}

/* 分页 */
.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sales-return-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .return-overview .el-col {
    margin-bottom: 12px;
  }

  .overview-content {
    padding: 16px;
  }

  .search-card .el-form {
    flex-direction: column;
  }

  .search-card .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;
  }

  .card-view .el-col {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-left h2 {
    font-size: 24px;
  }

  .overview-value {
    font-size: 20px;
  }

  .overview-content {
    gap: 12px;
  }

  .overview-icon {
    padding: 8px;
  }
}

/* 表单相关样式 */
.order-mode-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
}

.custom-table {
  min-width: 1200px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-weight: 600;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #dcdfe6;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 12px 8px;
  border-right: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.product {
  flex: 2;
  min-width: 200px;
}

.table-cell.batch {
  flex: 1;
  min-width: 120px;
}

.table-cell.warehouse {
  flex: 1;
  min-width: 120px;
}

.table-cell.quantity {
  flex: 1.2;
  min-width: 140px;
}

.table-cell.unit-price {
  flex: 1;
  min-width: 120px;
}

.table-cell.total-price {
  flex: 1;
  min-width: 120px;
}

.table-cell.quality {
  flex: 1.5;
  min-width: 150px;
}

.table-cell.actions {
  flex: 0.8;
  min-width: 80px;
  justify-content: center;
}

.price-display {
  font-weight: 600;
  color: #e6a23c;
}

.quantity-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.available-hint {
  color: #409eff;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  line-height: 1;
}

.empty-state {
  padding: 40px;
  text-align: center;
  color: #909399;
}

.total-section {
  margin-top: 16px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
  text-align: right;
}

.total-label {
  font-size: 16px;
  font-weight: 500;
  margin-right: 8px;
}

.total-amount {
  font-size: 20px;
  font-weight: 600;
  color: #e6a23c;
}

.dialog-footer {
  text-align: right;
}
</style>