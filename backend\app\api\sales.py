"""
销售订单API
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from datetime import datetime
from app.core.database import get_db
from app.services.sales_service import SalesService
from app.schemas.sales import (
    SalesOrderResponse, SalesOrderCreate, SalesOrderQuery,
    SalesOrderStats, SalesOrderListResponse, SalesOrderStatus, Platform,
    SalesOutboundResponse, SalesOutboundCreate, SalesOutboundStats,
    AvailableSalesOrderResponse,
    SalesReturn as SalesReturnSchema,
    SalesReturnCreate, SalesReturnUpdate, SalesReturnStatusUpdate,
    SalesReturnStats
)
from app.models.sales import SalesReturn, SalesReturnItem, SalesReturnStatus, SalesOrder, SalesOutbound
from app.models.customer import Customer
from app.core.utils import generate_order_number
from app.api.auth import get_current_user

router = APIRouter()



@router.get("/")
async def get_sales_orders(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    order_no: Optional[str] = Query(None, description="订单号"),
    customer_id: Optional[int] = Query(None, description="客户ID"),
    status: Optional[SalesOrderStatus] = Query(None, description="订单状态"),
    sales_person: Optional[str] = Query(None, description="销售员"),
    platform: Optional[Platform] = Query(None, description="销售平台"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    available_only: Optional[str] = Query(None, description="只获取可用订单"),
    db: Session = Depends(get_db)
):
    """获取销售订单列表"""
    
    service = SalesService(db)

    # 如果只获取可用订单
    if available_only and available_only.lower() in ['true', '1', 'yes']:
        try:
            orders = service.get_available_sales_orders(customer_id)
            return orders
        except Exception as e:
            print(f"获取可用订单失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"获取可用订单失败: {str(e)}")

    # 构建查询参数
    query_params = SalesOrderQuery(
        order_no=order_no,
        customer_id=customer_id,
        status=status,
        sales_person=sales_person,
        platform=platform,
        start_date=start_date,
        end_date=end_date
    )

    # 获取数据
    skip = (page - 1) * page_size
    orders, total = service.get_sales_orders(skip=skip, limit=page_size, query=query_params)
    
    # 转换响应数据
    items = []
    for order in orders:
        # 转换订单明细
        order_items = []
        for item in order.items:
            item_data = {
                "id": item.id,
                "order_id": item.order_id,
                "line_number": item.line_number or 1,  # 提供默认值
                "product_id": item.product_id,
                "product_name": item.product_name,
                "product_sku": item.product_sku,
                "quantity": item.quantity,
                "unit_price": item.unit_price,
                "total_price": item.total_price,
                "shipped_quantity": item.shipped_quantity or 0,
                "created_at": item.created_at,
                "updated_at": None  # SalesOrderItem模型中没有updated_at字段
            }
            order_items.append(item_data)
        
        # 转换订单数据
        order_data = {
            "id": order.id,
            "order_no": order.order_no,
            "customer_id": order.customer_id,
            "customer_name": order.customer.name if order.customer else "",
            "total_amount": order.total_amount,
            "status": order.status,
            "delivery_date": order.delivery_date,
            "delivery_address": order.delivery_address,
            "sales_person": order.sales_person,
            "platform": order.platform,
            "original_order_no": order.original_order_no,
            "remark": order.remark,
            "created_at": order.created_at,
            "updated_at": order.updated_at,
            "items": order_items
        }
        items.append(SalesOrderResponse(**order_data))
    
    # 计算分页信息
    pages = (total + page_size - 1) // page_size
    
    return SalesOrderListResponse(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        pages=pages
    )


@router.get("/stats", response_model=SalesOrderStats)
async def get_sales_stats(db: Session = Depends(get_db)):
    """获取销售订单统计"""
    
    service = SalesService(db)
    return service.get_sales_stats()



@router.get("/{order_id}", response_model=SalesOrderResponse)
async def get_sales_order(order_id: int, db: Session = Depends(get_db)):
    """获取单个销售订单"""
    
    service = SalesService(db)
    order = service.get_sales_order(order_id)
    
    if not order:
        raise HTTPException(status_code=404, detail="销售订单不存在")
    
    # 转换订单明细
    order_items = []
    for item in order.items:
        item_data = {
            "id": item.id,
            "order_id": item.order_id,
            "line_number": item.line_number or 1,  # 提供默认值
            "product_id": item.product_id,
            "product_name": item.product_name,
            "product_sku": item.product_sku,
            "quantity": item.quantity,
            "unit_price": item.unit_price,
            "total_price": item.total_price,
            "shipped_quantity": item.shipped_quantity or 0,
            "created_at": item.created_at,
            "updated_at": None  # SalesOrderItem模型中没有updated_at字段
        }
        order_items.append(item_data)
    
    # 转换订单数据
    order_data = {
        "id": order.id,
        "order_no": order.order_no,
        "customer_id": order.customer_id,
        "customer_name": order.customer.name if order.customer else "",
        "total_amount": order.total_amount,
        "status": order.status,
        "delivery_date": order.delivery_date,
        "delivery_address": order.delivery_address,
        "sales_person": order.sales_person,
        "platform": order.platform,
        "original_order_no": order.original_order_no,
        "remark": order.remark,
        "created_at": order.created_at,
        "updated_at": order.updated_at,
        "items": order_items
    }
    
    return SalesOrderResponse(**order_data)


@router.post("/", response_model=SalesOrderResponse)
async def create_sales_order(order_data: SalesOrderCreate, db: Session = Depends(get_db)):
    """创建销售订单"""

    service = SalesService(db)

    try:
        order = service.create_sales_order(order_data)
        
        # 转换订单明细
        order_items = []
        for item in order.items:
            item_data = {
                "id": item.id,
                "order_id": item.order_id,
                "line_number": item.line_number,
                "product_id": item.product_id,
                "product_name": item.product_name,
                "product_sku": item.product_sku,
                "quantity": item.quantity,
                "unit_price": item.unit_price,
                "total_price": item.total_price,
                "shipped_quantity": item.shipped_quantity or 0,
                "created_at": item.created_at,
                "updated_at": None  # SalesOrderItem模型中没有updated_at字段
            }
            order_items.append(item_data)
        
        # 转换订单数据
        order_data = {
            "id": order.id,
            "order_no": order.order_no,
            "customer_id": order.customer_id,
            "customer_name": order.customer.name if order.customer else "",
            "total_amount": order.total_amount,
            "status": order.status,
            "delivery_date": order.delivery_date,
            "delivery_address": order.delivery_address,
            "sales_person": order.sales_person,
            "platform": order.platform,
            "original_order_no": order.original_order_no,
            "remark": order.remark,
            "created_at": order.created_at,
            "updated_at": order.updated_at,
            "items": order_items
        }
        
        return SalesOrderResponse(**order_data)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{order_id}", response_model=SalesOrderResponse)
async def update_sales_order(
    order_id: int,
    order_data: SalesOrderCreate,  # 使用SalesOrderCreate以支持明细更新
    db: Session = Depends(get_db)
):
    """更新销售订单（包括明细）"""

    service = SalesService(db)

    try:
        order = service.update_sales_order_with_items(order_id, order_data)

        if not order:
            raise HTTPException(status_code=404, detail="销售订单不存在")

        # 转换订单明细
        order_items = []
        for item in order.items:
            item_data = {
                "id": item.id,
                "order_id": item.order_id,
                "line_number": item.line_number or 1,  # 提供默认值
                "product_id": item.product_id,
                "product_name": item.product_name,
                "product_sku": item.product_sku,
                "quantity": item.quantity,
                "unit_price": item.unit_price,
                "total_price": item.total_price,
                "shipped_quantity": item.shipped_quantity or 0,
                "created_at": item.created_at,
                "updated_at": None  # SalesOrderItem模型中没有updated_at字段
            }
            order_items.append(item_data)

        # 转换订单数据
        response_data = {
            "id": order.id,
            "order_no": order.order_no,
            "customer_id": order.customer_id,
            "customer_name": order.customer.name if order.customer else "",
            "total_amount": order.total_amount,
            "status": order.status,
            "delivery_date": order.delivery_date,
            "delivery_address": order.delivery_address,
            "sales_person": order.sales_person,
            "platform": order.platform,
            "original_order_no": order.original_order_no,
            "remark": order.remark,
            "created_at": order.created_at,
            "updated_at": order.updated_at,
            "items": order_items
        }

        return SalesOrderResponse(**response_data)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{order_id}")
async def delete_sales_order(order_id: int, db: Session = Depends(get_db)):
    """删除销售订单"""
    
    service = SalesService(db)
    
    try:
        success = service.delete_sales_order(order_id)
        if not success:
            raise HTTPException(status_code=404, detail="销售订单不存在")
        
        return {"message": "销售订单删除成功"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch("/{order_id}/status")
async def update_order_status(
    order_id: int, 
    status: SalesOrderStatus,
    db: Session = Depends(get_db)
):
    """更新订单状态"""
    
    service = SalesService(db)
    order = service.update_order_status(order_id, status)
    
    if not order:
        raise HTTPException(status_code=404, detail="销售订单不存在")
    
    return {"message": "订单状态更新成功", "status": order.status}


# ==================== 销售出库单相关API ====================

@router.get("/outbounds/stats", response_model=SalesOutboundStats)
async def get_sales_outbound_stats(db: Session = Depends(get_db)):
    """获取销售出库单统计"""

    service = SalesService(db)
    return service.get_sales_outbound_stats()


@router.get("/outbounds/")
async def get_sales_outbounds(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    outbound_no: Optional[str] = Query(None, description="出库单号"),
    sales_order_no: Optional[str] = Query(None, description="销售订单号"),
    customer_id: Optional[int] = Query(None, description="客户ID"),
    warehouse_id: Optional[int] = Query(None, description="仓库ID"),
    status: Optional[str] = Query(None, description="出库状态"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """获取销售出库单列表"""

    service = SalesService(db)

    # 获取数据
    skip = (page - 1) * page_size
    outbounds, total = service.get_sales_outbounds(
        skip=skip,
        limit=page_size,
        outbound_no=outbound_no,
        sales_order_no=sales_order_no,
        customer_id=customer_id,
        warehouse_id=warehouse_id,
        status=status,
        start_date=start_date,
        end_date=end_date
    )

    # 转换响应数据
    result = []
    for outbound in outbounds:
        # 转换出库单明细
        outbound_items = []
        for item in outbound.items:
            item_data = {
                "id": item.id,
                "outbound_id": item.outbound_id,
                "sales_order_item_id": getattr(item, 'sales_order_item_id', None),
                "product_id": item.product_id,
                "product_name": item.product_name,
                "product_sku": item.product_sku,
                "warehouse_id": item.warehouse_id,
                "warehouse_name": item.warehouse.name if item.warehouse else "",
                "batch_no": item.batch_no,  # 添加批次号字段
                "quantity": item.quantity,
                "created_at": item.created_at
            }
            outbound_items.append(item_data)

        # 转换出库单数据
        outbound_data = {
            "id": outbound.id,
            "outbound_no": outbound.outbound_no,
            "sales_order_id": outbound.sales_order_id,
            "sales_order_no": outbound.sales_order_no,
            "customer_id": outbound.customer_id,
            "customer_name": outbound.customer.name if outbound.customer else "",
            "warehouse_id": None,  # 仓库信息现在在明细中
            "warehouse_name": "",  # 仓库信息现在在明细中
            "status": outbound.status,
            "outbound_date": outbound.outbound_date,
            "created_by": outbound.created_by,
            "operator": getattr(outbound, 'operator', None),
            "remark": outbound.remark,
            "created_at": outbound.created_at,
            "updated_at": outbound.updated_at,
            "items": outbound_items
        }
        result.append(outbound_data)

    return result


@router.get("/outbounds/available-for-return")
async def get_available_outbounds_for_return(
    customer_id: Optional[int] = Query(None, description="客户ID"),
    db: Session = Depends(get_db)
):
    """获取可用于退货的销售出库单"""
    from app.models.sales import SalesOutbound, SalesOutboundItem
    from sqlalchemy.orm import joinedload
    from sqlalchemy import and_

    # 查询已完成的出库单，并且至少有一个明细项还有可退货数量
    query = db.query(SalesOutbound).options(
        joinedload(SalesOutbound.items),
        joinedload(SalesOutbound.customer)
    ).join(SalesOutboundItem).filter(
        and_(
            SalesOutbound.status == "completed",  # 只有已完成的出库单才能退货
            SalesOutboundItem.outbound_quantity > SalesOutboundItem.returned_quantity  # 有可退货数量
        )
    )

    if customer_id:
        query = query.filter(SalesOutbound.customer_id == customer_id)

    # 去重并按创建时间倒序排列
    outbounds = query.distinct().order_by(SalesOutbound.created_at.desc()).limit(100).all()

    result = []
    for outbound in outbounds:
        outbound_dict = {
            "id": outbound.id,
            "outbound_no": outbound.outbound_no,
            "customer_id": outbound.customer_id,
            "customer_name": outbound.customer.name if outbound.customer else "",
            "outbound_date": outbound.outbound_date.isoformat() if outbound.outbound_date else None,
            "created_at": outbound.created_at.isoformat() if outbound.created_at else None,
            "items": []
        }

        # 添加出库明细
        for item in outbound.items:
            outbound_dict["items"].append({
                "id": item.id,
                "product_id": item.product_id,
                "product_name": item.product_name,
                "product_sku": item.product_sku,
                "warehouse_id": item.warehouse_id,
                "batch_no": item.batch_no,
                "quantity": item.quantity,
                "outbound_quantity": item.outbound_quantity or item.quantity
            })

        result.append(outbound_dict)

    return result


@router.get("/outbounds/{outbound_id}", response_model=SalesOutboundResponse)
async def get_sales_outbound(outbound_id: int, db: Session = Depends(get_db)):
    """获取单个销售出库单"""

    service = SalesService(db)
    outbound = service.get_sales_outbound(outbound_id)

    if not outbound:
        raise HTTPException(status_code=404, detail="销售出库单不存在")

    # 转换出库单明细
    outbound_items = []
    for item in outbound.items:
        item_data = {
            "id": item.id,
            "outbound_id": item.outbound_id,
            "sales_order_item_id": getattr(item, 'sales_order_item_id', None),
            "product_id": item.product_id,
            "product_name": item.product_name,
            "product_sku": item.product_sku,
            "warehouse_id": item.warehouse_id,
            "warehouse_name": item.warehouse.name if item.warehouse else "",
            "batch_no": item.batch_no,  # 添加批次号字段
            "quantity": item.quantity,
            "created_at": item.created_at
        }
        outbound_items.append(item_data)

    # 转换出库单数据
    outbound_data = {
        "id": outbound.id,
        "outbound_no": outbound.outbound_no,
        "sales_order_id": outbound.sales_order_id,
        "sales_order_no": outbound.sales_order_no,
        "customer_id": outbound.customer_id,
        "customer_name": outbound.customer.name if outbound.customer else "",
        "warehouse_id": None,  # 仓库信息现在在明细中
        "warehouse_name": "",  # 仓库信息现在在明细中
        "status": outbound.status,
        "outbound_date": outbound.outbound_date,
        "operator": getattr(outbound, 'operator', None),
        "remark": outbound.remark,
        "created_by": outbound.created_by,  # 添加创建人字段
        "created_at": outbound.created_at,
        "updated_at": outbound.updated_at,
        "items": outbound_items
    }

    return SalesOutboundResponse(**outbound_data)


@router.post("/outbounds/", response_model=SalesOutboundResponse)
async def create_sales_outbound(outbound_data: SalesOutboundCreate, db: Session = Depends(get_db)):
    """创建销售出库单"""

    service = SalesService(db)

    try:
        print(f"📥 接收到出库单数据: {outbound_data}")

        # 从认证信息中获取当前用户，如果没有则使用系统用户
        created_by = "system"

        outbound = service.create_sales_outbound(outbound_data, created_by)

        # 转换出库单明细
        outbound_items = []
        for item in outbound.items:
            item_data = {
                "id": item.id,
                "outbound_id": item.outbound_id,
                "sales_order_item_id": getattr(item, 'sales_order_item_id', None),
                "product_id": item.product_id,
                "product_name": item.product_name,
                "product_sku": item.product_sku,
                "warehouse_id": item.warehouse_id,
                "warehouse_name": item.warehouse.name if item.warehouse else "",
                "batch_no": item.batch_no,
                "quantity": item.quantity,
                "created_at": item.created_at
            }
            outbound_items.append(item_data)

        # 转换出库单数据
        response_data = {
            "id": outbound.id,
            "outbound_no": outbound.outbound_no,
            "sales_order_id": outbound.sales_order_id,
            "sales_order_no": outbound.sales_order_no,
            "customer_id": outbound.customer_id,
            "customer_name": outbound.customer.name if outbound.customer else "",
            "warehouse_id": None,  # 仓库信息现在在明细中
            "warehouse_name": "",  # 仓库信息现在在明细中
            "status": outbound.status,
            "outbound_date": outbound.outbound_date,
            "operator": getattr(outbound, 'operator', None),
            "remark": outbound.remark,
            "created_by": outbound.created_by,
            "created_at": outbound.created_at,
            "updated_at": outbound.updated_at,
            "items": outbound_items
        }

        return SalesOutboundResponse(**response_data)

    except ValueError as e:
        print(f"❌ 创建出库单失败 (ValueError): {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"❌ 创建出库单失败 (Exception): {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")


@router.put("/outbounds/{outbound_id}", response_model=SalesOutboundResponse)
async def update_sales_outbound(
    outbound_id: int,
    outbound_data: SalesOutboundCreate,
    db: Session = Depends(get_db)
):
    """更新销售出库单"""

    service = SalesService(db)

    try:
        outbound = service.update_sales_outbound(outbound_id, outbound_data)

        if not outbound:
            raise HTTPException(status_code=404, detail="销售出库单不存在")

        # 转换出库单明细
        outbound_items = []
        for item in outbound.items:
            item_data = {
                "id": item.id,
                "outbound_id": item.outbound_id,
                "sales_order_item_id": getattr(item, 'sales_order_item_id', None),
                "product_id": item.product_id,
                "product_name": item.product_name,
                "product_sku": item.product_sku,
                "warehouse_id": item.warehouse_id,
                "warehouse_name": item.warehouse.name if item.warehouse else "",
                "batch_no": item.batch_no,  # 添加批次号字段
                "quantity": item.quantity,
                "created_at": item.created_at
            }
            outbound_items.append(item_data)

        # 转换出库单数据
        response_data = {
            "id": outbound.id,
            "outbound_no": outbound.outbound_no,
            "sales_order_id": outbound.sales_order_id,
            "sales_order_no": outbound.sales_order_no,
            "customer_id": outbound.customer_id,
            "customer_name": outbound.customer.name if outbound.customer else "",
            "warehouse_id": None,  # 仓库信息现在在明细中
            "warehouse_name": "",  # 仓库信息现在在明细中
            "status": outbound.status,
            "outbound_date": outbound.outbound_date,
            "operator": getattr(outbound, 'operator', None),
            "remark": outbound.remark,
            "created_by": outbound.created_by,  # 添加创建人字段
            "created_at": outbound.created_at,
            "updated_at": outbound.updated_at,
            "items": outbound_items
        }

        return SalesOutboundResponse(**response_data)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/outbounds/{outbound_id}")
async def delete_sales_outbound(outbound_id: int, db: Session = Depends(get_db)):
    """删除销售出库单"""

    service = SalesService(db)

    try:
        success = service.delete_sales_outbound(outbound_id)
        if not success:
            raise HTTPException(status_code=404, detail="销售出库单不存在")

        return {"message": "销售出库单删除成功"}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/outbounds/{outbound_id}/submit")
async def submit_sales_outbound(outbound_id: int, db: Session = Depends(get_db)):
    """提交销售出库单"""

    service = SalesService(db)

    try:
        # 从认证信息中获取当前用户，如果没有则使用系统用户
        submitted_by = "system"

        outbound = service.submit_sales_outbound(outbound_id, submitted_by)
        if not outbound:
            raise HTTPException(status_code=404, detail="销售出库单不存在")

        return {"message": "销售出库单提交成功", "status": outbound.status}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/outbounds/{outbound_id}/approve")
async def approve_sales_outbound(outbound_id: int, db: Session = Depends(get_db)):
    """审核通过销售出库单"""

    service = SalesService(db)

    try:
        # 从认证信息中获取当前用户，如果没有则使用系统用户
        approved_by = "system"

        outbound = service.approve_sales_outbound(outbound_id, approved_by)
        if not outbound:
            raise HTTPException(status_code=404, detail="销售出库单不存在")

        return {"message": "销售出库单审核通过", "status": outbound.status}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))





@router.post("/outbounds/{outbound_id}/reject")
async def reject_sales_outbound(
    outbound_id: int,
    reject_reason: str = Query(..., description="拒绝原因"),
    db: Session = Depends(get_db)
):
    """审核拒绝销售出库单"""

    service = SalesService(db)

    try:
        # 从认证信息中获取当前用户，如果没有则使用系统用户
        rejected_by = "system"

        outbound = service.reject_sales_outbound(outbound_id, rejected_by, reject_reason)
        if not outbound:
            raise HTTPException(status_code=404, detail="销售出库单不存在")

        return {"message": "销售出库单审核拒绝", "status": outbound.status}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/outbounds/{outbound_id}/revoke")
async def revoke_sales_outbound(outbound_id: int, db: Session = Depends(get_db)):
    """撤销销售出库单（从已提交状态回到草稿状态）"""

    service = SalesService(db)

    try:
        # 从认证信息中获取当前用户，如果没有则使用系统用户
        revoked_by = "system"

        outbound = service.revoke_sales_outbound(outbound_id, revoked_by)
        if not outbound:
            raise HTTPException(status_code=404, detail="销售出库单不存在")

        return {"message": "销售出库单撤销成功", "status": outbound.status}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/outbounds/{outbound_id}/cancel-delivery")
async def cancel_delivery(
    outbound_id: int,
    cancel_reason: str = Query(..., description="取消原因"),
    db: Session = Depends(get_db)
):
    """取消发货"""

    service = SalesService(db)

    try:
        # 从认证信息中获取当前用户，如果没有则使用系统用户
        cancelled_by = "system"

        outbound = service.cancel_delivery(outbound_id, cancelled_by, cancel_reason)
        if not outbound:
            raise HTTPException(status_code=404, detail="销售出库单不存在")

        return {"message": "发货已取消", "status": outbound.status}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/outbounds/{outbound_id}/complete")
async def complete_sales_outbound(outbound_id: int, db: Session = Depends(get_db)):
    """确认完成销售出库单"""

    service = SalesService(db)

    try:
        # 从认证信息中获取当前用户，如果没有则使用系统用户
        completed_by = "system"

        outbound = service.complete_sales_outbound(outbound_id, completed_by)
        if not outbound:
            raise HTTPException(status_code=404, detail="销售出库单不存在")

        return {"message": "销售出库单已完成", "status": outbound.status}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))





# ==================== 销售退货单相关API ====================

def get_sales_return_stats(db: Session) -> SalesReturnStats:
    """获取销售退货单统计信息"""
    from sqlalchemy import func

    # 基础统计
    total_returns = db.query(SalesReturn).count()
    draft_returns = db.query(SalesReturn).filter(SalesReturn.status == SalesReturnStatus.DRAFT).count()
    submitted_returns = db.query(SalesReturn).filter(SalesReturn.status == SalesReturnStatus.SUBMITTED).count()
    approved_returns = db.query(SalesReturn).filter(SalesReturn.status == SalesReturnStatus.APPROVED).count()
    returned_returns = db.query(SalesReturn).filter(SalesReturn.status == SalesReturnStatus.RETURNED).count()
    completed_returns = db.query(SalesReturn).filter(SalesReturn.status == SalesReturnStatus.COMPLETED).count()

    # 金额统计
    total_amount = db.query(func.sum(SalesReturn.total_amount)).scalar() or 0

    return SalesReturnStats(
        total_returns=total_returns,
        draft_returns=draft_returns,
        submitted_returns=submitted_returns,
        approved_returns=approved_returns,
        returned_returns=returned_returns,
        completed_returns=completed_returns,
        total_amount=total_amount
    )


@router.get("/returns/stats", response_model=SalesReturnStats)
def get_return_stats(db: Session = Depends(get_db)):
    """获取销售退货单统计信息"""
    return get_sales_return_stats(db)


@router.get("/returns/", response_model=List[SalesReturnSchema])
def get_sales_returns(
    return_no: Optional[str] = Query(None, description="退货单号"),
    customer_id: Optional[int] = Query(None, description="客户ID"),
    status: Optional[SalesReturnStatus] = Query(None, description="状态"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    sort_by: Optional[str] = Query(None, description="排序字段"),
    sort_order: Optional[str] = Query(None, description="排序方式 (asc/desc)"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取销售退货单列表"""
    from sqlalchemy.orm import joinedload

    query = db.query(SalesReturn).options(
        joinedload(SalesReturn.customer),
        joinedload(SalesReturn.sales_outbound)
    )

    # 应用筛选条件
    if return_no:
        query = query.filter(SalesReturn.return_no.contains(return_no))
    if customer_id:
        query = query.filter(SalesReturn.customer_id == customer_id)
    if status:
        query = query.filter(SalesReturn.status == status)
    if start_date:
        query = query.filter(SalesReturn.return_date >= start_date)
    if end_date:
        query = query.filter(SalesReturn.return_date <= end_date)

    # 应用排序
    if sort_by:
        # 确保排序字段是安全的
        allowed_sort_fields = ['created_at', 'return_no', 'total_amount', 'return_date']
        if sort_by in allowed_sort_fields:
            sort_column = getattr(SalesReturn, sort_by)
            if sort_order and sort_order.lower() == 'desc':
                sort_column = sort_column.desc()
            query = query.order_by(sort_column)
        else:
            # 默认按创建时间倒序排列
            query = query.order_by(SalesReturn.created_at.desc())
    else:
        # 默认按创建时间倒序排列
        query = query.order_by(SalesReturn.created_at.desc())

    # 分页
    offset = (page - 1) * page_size
    returns = query.offset(offset).limit(page_size).all()

    # 添加关联信息
    result = []
    for return_obj in returns:
        try:
            return_dict = return_obj.__dict__.copy()

            # 移除SQLAlchemy内部属性
            return_dict.pop('_sa_instance_state', None)

            # 添加客户名称
            if return_obj.customer:
                return_dict['customer_name'] = return_obj.customer.name
            else:
                return_dict['customer_name'] = None

            # 添加销售出库单号
            if return_obj.sales_outbound:
                return_dict['sales_outbound_no'] = return_obj.sales_outbound.outbound_no
            else:
                return_dict['sales_outbound_no'] = None

            # 确保明细列表为空（避免循环引用）
            return_dict['items'] = []

            validated_return = SalesReturnSchema.model_validate(return_dict)
            result.append(validated_return)

        except Exception as e:
            print(f"❌ 验证退货单 {return_obj.return_no} 失败: {e}")
            print(f"数据: {return_dict}")
            raise HTTPException(status_code=500, detail=f"数据验证失败: {str(e)}")

    return result


@router.get("/returns/{return_id}", response_model=SalesReturnSchema)
def get_sales_return(return_id: int, db: Session = Depends(get_db)):
    """获取销售退货单详情"""
    from sqlalchemy.orm import joinedload

    return_obj = db.query(SalesReturn).options(
        joinedload(SalesReturn.customer),
        joinedload(SalesReturn.sales_outbound),
        joinedload(SalesReturn.items)
    ).filter(SalesReturn.id == return_id).first()

    if not return_obj:
        raise HTTPException(status_code=404, detail="销售退货单不存在")

    return_dict = return_obj.__dict__.copy()

    # 添加关联信息
    if return_obj.customer:
        return_dict['customer_name'] = return_obj.customer.name
    if return_obj.sales_outbound:
        return_dict['sales_outbound_no'] = return_obj.sales_outbound.outbound_no

    return SalesReturnSchema.model_validate(return_dict)


@router.post("/returns/", response_model=SalesReturnSchema)
def create_sales_return(
    return_data: SalesReturnCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建销售退货单"""
    # 验证客户是否存在
    customer = db.query(Customer).filter(Customer.id == return_data.customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客户不存在")

    # 验证销售出库单是否存在（如果提供）
    if return_data.sales_outbound_id:
        from app.models.sales import SalesOutbound as SalesOutboundModel
        outbound = db.query(SalesOutboundModel).filter(SalesOutboundModel.id == return_data.sales_outbound_id).first()
        if not outbound:
            raise HTTPException(status_code=404, detail="销售出库单不存在")

    # 生成退货单号
    if not return_data.return_no:
        return_data.return_no = generate_order_number("SR")

    # 创建退货单
    db_return = SalesReturn(
        return_no=return_data.return_no,
        sales_outbound_id=return_data.sales_outbound_id,
        customer_id=return_data.customer_id,
        return_date=return_data.return_date,
        reason=return_data.reason,
        total_amount=return_data.total_amount,
        remark=return_data.remark,
        status=SalesReturnStatus.DRAFT,
        created_by=current_user.get("username")   # 只记录创建人
    )

    db.add(db_return)
    db.flush()  # 获取ID

    # 创建退货明细
    for item_data in return_data.items:
        db_item = SalesReturnItem(
            sales_return_id=db_return.id,
            product_id=item_data.product_id,
            product_name=item_data.product_name,
            product_sku=item_data.product_sku,
            batch_no=getattr(item_data, 'batch_no', None),
            warehouse_id=getattr(item_data, 'warehouse_id', None),
            return_quantity=item_data.return_quantity,
            unit_price=item_data.unit_price,
            total_price=item_data.total_price,
            quality_issue=item_data.quality_issue
        )
        db.add(db_item)

    db.commit()
    db.refresh(db_return)

    return_dict = db_return.__dict__.copy()
    # 移除SQLAlchemy内部属性
    return_dict.pop('_sa_instance_state', None)

    if db_return.customer:
        return_dict['customer_name'] = db_return.customer.name
    else:
        return_dict['customer_name'] = None

    if db_return.sales_outbound:
        return_dict['sales_outbound_no'] = db_return.sales_outbound.outbound_no
    else:
        return_dict['sales_outbound_no'] = None

    # 添加空的明细列表
    return_dict['items'] = []

    return SalesReturnSchema.model_validate(return_dict)


@router.put("/returns/{return_id}", response_model=SalesReturnSchema)
def update_sales_return(
    return_id: int,
    return_data: SalesReturnUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """更新销售退货单"""
    db_return = db.query(SalesReturn).filter(SalesReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="销售退货单不存在")

    # 只有草稿状态才能修改
    if db_return.status != SalesReturnStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的退货单才能修改")

    # 更新基本信息
    update_data = return_data.model_dump(exclude_unset=True)
    items_data = update_data.pop('items', None)

    for field, value in update_data.items():
        setattr(db_return, field, value)

    # 记录更新人
    db_return.updated_by = current_user.get("username")

    # 更新明细
    if items_data is not None:
        # 删除原有明细
        db.query(SalesReturnItem).filter(SalesReturnItem.sales_return_id == return_id).delete()

        # 创建新明细
        for item_data in items_data:
            db_item = SalesReturnItem(
                sales_return_id=return_id,
                **item_data
            )
            db.add(db_item)

    db.commit()
    db.refresh(db_return)

    return_dict = db_return.__dict__.copy()
    if db_return.customer:
        return_dict['customer_name'] = db_return.customer.name
    if db_return.sales_outbound:
        return_dict['sales_outbound_no'] = db_return.sales_outbound.outbound_no

    return SalesReturnSchema.model_validate(return_dict)


@router.delete("/returns/{return_id}")
def delete_sales_return(return_id: int, db: Session = Depends(get_db)):
    """删除销售退货单"""
    db_return = db.query(SalesReturn).filter(SalesReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="销售退货单不存在")

    # 只有草稿状态才能删除
    if db_return.status != SalesReturnStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的退货单才能删除")

    db.delete(db_return)
    db.commit()

    return {"message": "销售退货单删除成功"}


@router.post("/returns/{return_id}/submit")
def submit_sales_return(
    return_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """提交销售退货单"""
    db_return = db.query(SalesReturn).filter(SalesReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="销售退货单不存在")

    if db_return.status != SalesReturnStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的退货单才能提交")

    db_return.status = SalesReturnStatus.SUBMITTED
    db_return.submitted_at = datetime.now()
    db_return.submitted_by = current_user.get("username")  # 记录提交人
    db_return.updated_by = current_user.get("username")    # 记录更新人

    db.commit()

    return {"message": "销售退货单提交成功"}


@router.post("/returns/{return_id}/approve")
def approve_sales_return(
    return_id: int,
    status_data: SalesReturnStatusUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """审核销售退货单"""
    db_return = db.query(SalesReturn).filter(SalesReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="销售退货单不存在")

    if db_return.status != SalesReturnStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交的退货单才能审核")

    # 验证状态值 - 使用字符串值比较
    valid_status_values = [SalesReturnStatus.APPROVED.value, SalesReturnStatus.REJECTED.value]
    status_value = status_data.status.value if hasattr(status_data.status, 'value') else str(status_data.status)

    if status_value not in valid_status_values:
        raise HTTPException(
            status_code=400,
            detail=f"审核状态只能是已审核或已拒绝，收到: {status_value}，有效值: {valid_status_values}"
        )

    db_return.status = status_data.status
    db_return.approved_at = datetime.now()
    db_return.approved_by = current_user.get("username")  # 记录审核人
    db_return.approval_note = status_data.note
    db_return.updated_by = current_user.get("username")   # 记录更新人

    db.commit()

    action = "审核通过" if status_data.status == SalesReturnStatus.APPROVED else "审核拒绝"
    return {"message": f"销售退货单{action}"}


@router.post("/returns/{return_id}/return")
def return_sales_return(
    return_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """确认退货"""
    db_return = db.query(SalesReturn).filter(SalesReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="销售退货单不存在")

    if db_return.status != SalesReturnStatus.APPROVED:
        raise HTTPException(status_code=400, detail="只有已审核的退货单才能确认退货")

    db_return.status = SalesReturnStatus.RETURNED
    db_return.returned_at = datetime.now()
    db_return.returned_by = current_user.get("username")  # 记录退货人
    db_return.updated_by = current_user.get("username")   # 记录更新人

    # 更新库存（增加库存）- 销售退货意味着商品退回到仓库，需要增加库存
    from app.services.inventory_service import InventoryService
    inventory_service = InventoryService(db)

    for item in db_return.items:
        try:
            # 增加库存
            inventory_service.increase_stock(
                product_id=item.product_id,
                warehouse_id=item.warehouse_id,
                batch_no=item.batch_no or "DEFAULT",  # 如果没有批次号，使用默认值
                quantity=item.return_quantity,
                reason=f"销售退货确认: {db_return.return_no}",
                operator="system"
            )
        except Exception as e:
            # 如果库存调整失败，回滚事务
            db.rollback()
            raise HTTPException(status_code=400, detail=f"商品 {item.product_name} 库存调整失败: {str(e)}")

    db.commit()

    return {"message": "销售退货确认成功"}


@router.post("/returns/{return_id}/complete")
def complete_sales_return(
    return_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """完成销售退货单"""
    db_return = db.query(SalesReturn).filter(SalesReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="销售退货单不存在")

    if db_return.status != SalesReturnStatus.RETURNED:
        raise HTTPException(status_code=400, detail="只有已退货的退货单才能完成")

    # 如果退货单关联了出库单，需要更新出库单的已退货数量
    if db_return.sales_outbound_id:
        from app.models.sales import SalesOutboundItem

        # 获取退货明细
        return_items = db.query(SalesReturnItem).filter(
            SalesReturnItem.sales_return_id == return_id
        ).all()

        # 更新出库单明细的已退货数量
        for return_item in return_items:
            # 查找对应的出库单明细
            outbound_item = db.query(SalesOutboundItem).filter(
                SalesOutboundItem.sales_outbound_id == db_return.sales_outbound_id,
                SalesOutboundItem.product_id == return_item.product_id,
                SalesOutboundItem.batch_no == return_item.batch_no,
                SalesOutboundItem.warehouse_id == return_item.warehouse_id
            ).first()

            if outbound_item:
                # 增加已退货数量
                if outbound_item.returned_quantity is None:
                    outbound_item.returned_quantity = 0
                outbound_item.returned_quantity += return_item.return_quantity

                # 确保已退货数量不超过出库数量
                if outbound_item.returned_quantity > outbound_item.outbound_quantity:
                    outbound_item.returned_quantity = outbound_item.outbound_quantity

    # 更新退货单状态
    db_return.status = SalesReturnStatus.COMPLETED
    db_return.updated_by = current_user.get("username")

    db.commit()

    return {"message": "销售退货单完成"}


@router.post("/returns/{return_id}/revoke")
def revoke_sales_return(
    return_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """撤销销售退货单（从已提交状态回到草稿状态）"""
    db_return = db.query(SalesReturn).filter(SalesReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="销售退货单不存在")

    if db_return.status != SalesReturnStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交的退货单才能撤销")

    db_return.status = SalesReturnStatus.DRAFT
    db_return.submitted_at = None
    db_return.submitted_by = None
    db_return.updated_by = current_user.get("username")   # 记录更新人

    db.commit()

    return {"message": "销售退货单撤销成功", "status": db_return.status}